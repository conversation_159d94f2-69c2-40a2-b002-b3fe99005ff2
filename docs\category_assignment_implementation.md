# Имплементация на присвояване на категории в Multi Feed Syncer

## Описание

Тази функционалност добавя автоматично присвояване на категории на продуктите при синхронизация в Multi Feed Syncer модула. Категориите се присвояват според предварително дефинираните съответствия в таблицата `multi_feed_syncer_categories_mapping`.

## Основни принципи

### 1. Условия за присвояване на категории

**При добавяне на нови продукти:**
- Винаги се присвояват категории според съответствията от таблицата
- Използват се CategoryBranch данните от синхронизиращия файл

**При обновяване на съществуващи продукти:**
- Категории се присвояват САМО на продукти, които в момента нямат зададена категория
- Продукти с вече зададени категории не се променят

### 2. Източник на данните

- **CategoryBranch данни**: Извличат се от `categories_data_source` полето в продуктовите данни
- **Формат на данните**: "Категория1|Категория2|Категория3" се преобразува в "Категория1 > Категория2 > Категория3"
- **Таблица за съответствия**: `multi_feed_syncer_categories_mapping` съдържа мапинг между source и target категории

### 3. Приоритет на съответствията

1. **Най-висок приоритет**: Ръчно дефинирани съответствия от таблицата `multi_feed_syncer_categories_mapping`
2. **Втори приоритет**: Ръчно дефинирани съответствия от конектора (getCategoryMappings())
3. **Най-нисък приоритет**: Автоматични алгоритми за мапиране

## Технически детайли

### Нови методи в `admin/model/extension/module/multi_feed_syncer.php`

#### `_assignCategoriesToProduct($product_id, $product_data, $mfsc_id, $is_new_product)`
- **Цел**: Присвоява категории на продукт според съответствията
- **Параметри**:
  - `$product_id`: ID на продукта
  - `$product_data`: Данни на продукта (включително categories_data_source)
  - `$mfsc_id`: ID на конектора
  - `$is_new_product`: true за нови продукти, false за съществуващи
- **Връща**: Масив с ID-та на категориите за присвояване

#### `_shouldAssignCategories($product_id)`
- **Цел**: Проверява дали продуктът трябва да получи категории
- **Логика**: Връща true ако продуктът няма зadadени категории
- **Използва се**: Само при обновяване на съществуващи продукти

#### `_extractCategoryBranches($product_data)`
- **Цел**: Извлича CategoryBranch данните от продуктовите данни
- **Източник**: `categories_data_source` масива от конектора
- **Връща**: Масив с category branch пътища

#### `_assignCategoriesToProductOptimized($product_id, $product_data, $mfsc_id)`
- **Цел**: Оптимизирана версия за присвояване на категории без проверка за съществуващи категории
- **Използва се**: Когато вече знаем че продуктът няма категории
- **Оптимизация**: Кешира съответствията на категории за по-добра производителност
- **Връща**: Масив с ID-та на категориите за присвояване

#### `_getCategoryIdFromPath($category_path)`
- **Цел**: Намира OpenCart category_id по пълен път на категорията
- **Логика**: Обхожда йерархията на категориите стъпка по стъпка
- **Връща**: ID на категорията или 0 ако не е намерена

### Модификации в съществуващи методи

#### `_batchInsertProducts()`
- **Добавено**: Присвояване на категории за всички нови продукти
- **Логика**: Винаги се опитва да присвои категории според съответствията
- **Таблица**: Вмъква записи в `product_to_category`

#### `_batchUpdateProducts()`
- **Добавено**: Присвояване на категории само за продукти без категории
- **Оптимизация**: Предварително извличане на всички продукти без категории с една SQL заявка
- **Логика**: Използва предварително зареден масив вместо индивидуални проверки
- **Таблица**: Вмъква записи в `product_to_category`

#### `_insertRelatedProductDataForChunk()`
- **Добавено**: Обработка на категории при добавяне на нови продукти
- **Статистики**: Отчита броя продукти с присвоени категории

## Примерен работен процес

### 1. Обработка на нов продукт
```
1. Продуктът се добавя в основната таблица
2. Извличат се CategoryBranch данните: "Хартия|Формуляри|Безопасност"
3. Преобразува се в: "Хартия > Формуляри > Безопасност"
4. Търси се съответствие в таблицата за мапиране
5. Ако има съответствие: "Офис > Хартия > Формуляри"
6. Намира се category_id на "Офис > Хартия > Формуляри"
7. Добавя се запис в product_to_category
```

### 2. Обработка на съществуващ продукт
```
1. Проверява се дали продуктът има категории
2. Ако НЯМА категории:
   - Извличат се CategoryBranch данните
   - Следва същия процес като за нов продукт
3. Ако ИМА категории:
   - Пропуска се присвояването на категории
```

## Логове и статистики

### Съобщения в лога
- "Присвоени категории на X продукта (порция за добавяне Y)"
- "Присвоени категории на X продукта при обновяване"
- "Грешка при присвояване на категории (порция X): [съобщение]"

### Статистики
- Брой продукти с присвоени категории при добавяне
- Брой продукти с присвоени категории при обновяване
- Детайлни логове за всяка операция

## Съвместимост

### Изисквания
- Таблица `multi_feed_syncer_categories_mapping` трябва да съществува
- Конекторите трябва да предоставят `categories_data_source` данни
- CategoryBranch данните трябва да са в правилния формат

### Обратна съвместимост
- Ако няма съответствия в таблицата, продуктите се обработват без категории
- Ако няма CategoryBranch данни, не се присвояват категории
- Съществуващите продукти с категории не се променят

## Тестване

За тестване на функционалността използвайте файла `test_category_assignment.php`, който демонстрира:
- Извличане на CategoryBranch данни
- Преобразуване на формата
- Мапиране според съответствията
- Резултати от процеса

## Оптимизация на производителността

### Проблем
При обновяване на големи обеми продукти (хиляди), старата логика изпълняваше по една SQL заявка за всеки продукт за проверка дали има категории.

### Решение
Оптимизирана логика която:
1. **Предварително извличане**: Една SQL заявка в началото за намиране на всички продукти без категории
2. **Кеширане в масив**: Резултатът се съхранява в масив за бързо търсене
3. **Бърза проверка**: Използва `isset()` вместо SQL заявки в цикъла

### Резултати
- **SQL заявки**: От N+M до 1+M (където N = общо продукти, M = продукти без категории)
- **Производителност**: До 90% намаляване на SQL заявките при големи обеми
- **Пример**: При 5000 продукта - от 6500 до 1501 SQL заявки

### SQL заявка за оптимизация
```sql
SELECT DISTINCT p.product_id
FROM `oc_product` p
LEFT JOIN `oc_product_to_category` ptc ON (p.product_id = ptc.product_id)
WHERE p.product_id IN (1, 2, 3, ...)
AND ptc.product_id IS NULL
```

## Бъдещи подобрения

1. **Множествени категории**: Поддръжка за присвояване на множество категории на един продукт
2. **Автоматично създаване**: Автоматично създаване на липсващи категории
3. **Приоритизиране**: Възможност за задаване на приоритет на категориите
4. **Валидация**: Допълнителна валидация на съответствията
