<?php
/**
 * Тест за оптимизацията на присвояване на категории в Multi Feed Syncer
 * Демонстрира разликата между старата и новата логика
 */

echo "<h1>Тест за оптимизация на присвояване на категории</h1>\n";

// Симулираме сценарий с голям брой продукти за обновяване
$total_products = 1000;
$products_without_categories = 300;

echo "<h2>Сценарий за тестване:</h2>\n";
echo "<ul>\n";
echo "<li><strong>Общо продукти за обновяване:</strong> {$total_products}</li>\n";
echo "<li><strong>Продукти без категории:</strong> {$products_without_categories}</li>\n";
echo "<li><strong>Продукти с категории:</strong> " . ($total_products - $products_without_categories) . "</li>\n";
echo "</ul>\n";

// Симулираме стария подход
echo "<h2>Стар подход (неоптимизиран):</h2>\n";
echo "<div style='background-color: #f8d7da; padding: 10px; border-radius: 5px;'>\n";
echo "<h3>Логика:</h3>\n";
echo "<ol>\n";
echo "<li>За всеки продукт в цикъла:</li>\n";
echo "<li>Извикай <code>_shouldAssignCategories(\$product_id)</code></li>\n";
echo "<li>Изпълни SQL заявка: <code>SELECT COUNT(*) FROM product_to_category WHERE product_id = X</code></li>\n";
echo "<li>Ако няма категории, присвои категории</li>\n";
echo "</ol>\n";
echo "<h3>SQL заявки:</h3>\n";
echo "<p><strong>Общо SQL заявки за проверка:</strong> {$total_products}</p>\n";
echo "<p><strong>SQL заявки за присвояване:</strong> {$products_without_categories}</p>\n";
echo "<p><strong>Общо SQL заявки:</strong> " . ($total_products + $products_without_categories) . "</p>\n";
echo "</div>\n";

// Симулираме новия подход
echo "<h2>Нов подход (оптимизиран):</h2>\n";
echo "<div style='background-color: #d4edda; padding: 10px; border-radius: 5px;'>\n";
echo "<h3>Логика:</h3>\n";
echo "<ol>\n";
echo "<li>В началото: Изпълни ЕДНА SQL заявка за намиране на всички продукти без категории</li>\n";
echo "<li>Съхрани резултата в масив <code>\$products_without_categories</code></li>\n";
echo "<li>За всеки продукт в цикъла:</li>\n";
echo "<li>Провери дали <code>isset(\$products_without_categories[\$product_id])</code></li>\n";
echo "<li>Ако да, присвои категории (без допълнителна SQL заявка)</li>\n";
echo "</ol>\n";
echo "<h3>SQL заявки:</h3>\n";
echo "<p><strong>SQL заявка за проверка:</strong> 1 (в началото)</p>\n";
echo "<p><strong>SQL заявки за присвояване:</strong> {$products_without_categories}</p>\n";
echo "<p><strong>Общо SQL заявки:</strong> " . (1 + $products_without_categories) . "</p>\n";
echo "</div>\n";

// Сравнение на производителността
echo "<h2>Сравнение на производителността:</h2>\n";
$old_queries = $total_products + $products_without_categories;
$new_queries = 1 + $products_without_categories;
$improvement = $old_queries - $new_queries;
$improvement_percent = round(($improvement / $old_queries) * 100, 2);

echo "<table border='1' style='border-collapse: collapse; width: 100%;'>\n";
echo "<tr style='background-color: #f8f9fa;'>\n";
echo "<th>Метрика</th><th>Стар подход</th><th>Нов подход</th><th>Подобрение</th>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>SQL заявки за проверка</td>\n";
echo "<td style='color: red;'>{$total_products}</td>\n";
echo "<td style='color: green;'>1</td>\n";
echo "<td style='color: green; font-weight: bold;'>-" . ($total_products - 1) . "</td>\n";
echo "</tr>\n";
echo "<tr>\n";
echo "<td>SQL заявки за присвояване</td>\n";
echo "<td>{$products_without_categories}</td>\n";
echo "<td>{$products_without_categories}</td>\n";
echo "<td>0 (без промяна)</td>\n";
echo "</tr>\n";
echo "<tr style='background-color: #e9ecef; font-weight: bold;'>\n";
echo "<td>Общо SQL заявки</td>\n";
echo "<td style='color: red;'>{$old_queries}</td>\n";
echo "<td style='color: green;'>{$new_queries}</td>\n";
echo "<td style='color: green;'>-{$improvement} ({$improvement_percent}% по-малко)</td>\n";
echo "</tr>\n";
echo "</table>\n";

// Демонстрация на SQL заявката
echo "<h2>Оптимизираща SQL заявка:</h2>\n";
echo "<div style='background-color: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;'>\n";
echo "<pre>\n";
echo "SELECT DISTINCT p.product_id \n";
echo "FROM `oc_product` p\n";
echo "LEFT JOIN `oc_product_to_category` ptc ON (p.product_id = ptc.product_id)\n";
echo "WHERE p.product_id IN (1, 2, 3, ..., {$total_products})\n";
echo "AND ptc.product_id IS NULL\n";
echo "</pre>\n";
echo "</div>\n";

echo "<h3>Обяснение на заявката:</h3>\n";
echo "<ul>\n";
echo "<li><strong>LEFT JOIN:</strong> Свързва продуктите с техните категории</li>\n";
echo "<li><strong>ptc.product_id IS NULL:</strong> Филтрира само продуктите БЕЗ категории</li>\n";
echo "<li><strong>IN (...):</strong> Ограничава търсенето само до продуктите за обновяване</li>\n";
echo "<li><strong>DISTINCT:</strong> Гарантира уникални product_id-та</li>\n";
echo "</ul>\n";

// Практически пример
echo "<h2>Практически пример с реални данни:</h2>\n";
echo "<div style='background-color: #fff3cd; padding: 10px; border-radius: 5px;'>\n";
echo "<h3>Сценарий: Синхронизация на 5000 продукта</h3>\n";
echo "<ul>\n";
echo "<li>Общо продукти: 5000</li>\n";
echo "<li>Продукти без категории: 1500 (30%)</li>\n";
echo "<li>Продукти с категории: 3500 (70%)</li>\n";
echo "</ul>\n";

$real_total = 5000;
$real_without = 1500;
$real_old_queries = $real_total + $real_without;
$real_new_queries = 1 + $real_without;
$real_improvement = $real_old_queries - $real_new_queries;
$real_improvement_percent = round(($real_improvement / $real_old_queries) * 100, 2);

echo "<p><strong>Стар подход:</strong> {$real_old_queries} SQL заявки</p>\n";
echo "<p><strong>Нов подход:</strong> {$real_new_queries} SQL заявки</p>\n";
echo "<p><strong>Спестени заявки:</strong> <span style='color: green; font-weight: bold;'>{$real_improvement} ({$real_improvement_percent}% по-малко)</span></p>\n";
echo "</div>\n";

// Заключение
echo "<h2>Заключение:</h2>\n";
echo "<div style='background-color: #d1ecf1; padding: 15px; border-radius: 5px;'>\n";
echo "<h3>✅ Предимства на оптимизацията:</h3>\n";
echo "<ul>\n";
echo "<li><strong>Драстично намаляване на SQL заявките:</strong> От N+M до 1+M (където N = общо продукти, M = продукти без категории)</li>\n";
echo "<li><strong>По-добра производителност:</strong> Особено при големи обеми данни</li>\n";
echo "<li><strong>Намалено натоварване на базата данни:</strong> По-малко заявки = по-малко ресурси</li>\n";
echo "<li><strong>По-бърза синхронизация:</strong> Особено забележимо при хиляди продукти</li>\n";
echo "<li><strong>Запазена функционалност:</strong> Същата логика, но по-ефективна</li>\n";
echo "</ul>\n";

echo "<h3>🔧 Технически детайли:</h3>\n";
echo "<ul>\n";
echo "<li>Използва се <code>LEFT JOIN</code> с <code>IS NULL</code> за намиране на продукти без категории</li>\n";
echo "<li>Резултатът се кешира в масив за бързо търсене с <code>isset()</code></li>\n";
echo "<li>Създаден е нов метод <code>_assignCategoriesToProductOptimized()</code> без проверка за категории</li>\n";
echo "<li>Добавено е кеширане на съответствията на категории за по-добра производителност</li>\n";
echo "</ul>\n";
echo "</div>\n";

?>
